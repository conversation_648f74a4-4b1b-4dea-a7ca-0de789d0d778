package org.easitline.common.db;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.EasyPool;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.log.JDBCLogger;

import com.alibaba.druid.pool.DruidDataSource;

public class ConnectionMetaData {

	  private String user;

	  private String password;

	  private String url;

	  private String appDatasourceName;
	  
	  private String appName;
	  
	  private String sysDatasourceName ;
	  
	  /** 连接工厂类型: 1-JDBC 2-datasource 3-sys 4-external */
	  private int   factoryType =1 ;
	  /** 外部连接引用 */
	  private Connection externalConnection;
	  /** 数据库类型缓存 */
	  private static final ConcurrentHashMap<String, DBTypes> DB_TYPE_CACHE = new ConcurrentHashMap<>();
	  /** 实例数据库类型缓存 */
	  private volatile DBTypes dbType;

	  public ConnectionMetaData(String sysDatasourceName) {
		this.factoryType = 3;
		this.sysDatasourceName = sysDatasourceName;
	  }
	  
	  public ConnectionMetaData( String appName,String appDatasourceName) {
		this.factoryType = 2;
		this.appDatasourceName = appDatasourceName;
		this.appName = appName;
	  }
	  
	  public ConnectionMetaData( String url,String user, String password) {
		this.factoryType = 1;
		this.user = user;
		this.password = password;
		this.url = url;
	 }

	  /**
	   * 基于外部连接的构造函数
	   * @param externalConnection 外部数据库连接
	   */
	  public ConnectionMetaData(Connection externalConnection) {
		this.factoryType = 4;
		this.externalConnection = externalConnection;
	  }
	  
	
	public String getAppName() {
		return appName;
	}
	
	public String getSysDatasourceName() {
		return sysDatasourceName;
	}
	
	public String getAppDatasourceName() {
		return appDatasourceName;
	}
	
	/** 获取数据库连接 */
	public Connection getConnection() throws SQLException{
		 Connection conn = null;
		 if (this.factoryType == 1) {
			 conn =  java.sql.DriverManager.getConnection(this.url,this.user,this.password);
		 } else if (this.factoryType == 4) {
			 // 外部连接模式，直接返回外部连接
			 if (this.externalConnection == null) {
				 throw new SQLException("外部连接未初始化");
			 }
			 if (this.externalConnection.isClosed()) {
				 throw new SQLException("外部连接已关闭");
			 }
			 return this.externalConnection;
		 } else {
			 conn = getDruidDataSource().getConnection();
		 }
		 return conn;
	}
	
	public DruidDataSource getDruidDataSource() throws SQLException {
		if (this.factoryType == 2) {
			 AppContext appContext = AppContext.getContext(this.appName);
			 String sysDsName  =  appContext.getDatasourceName(this.appDatasourceName);
			 this.sysDatasourceName = sysDsName;
			 return EasyPool.getInstance().getDruidDatasource(sysDsName);
		}
		 if(this.factoryType == 3){ 
			 return EasyPool.getInstance().getDruidDatasource(sysDatasourceName);
		 }
		return null;
	}
	
	
	public DBTypes getDriverType() {
		if (dbType != null) {
			return dbType;
		}

		synchronized (this) {
			if (dbType != null) {
				return dbType;
			}

			String cacheKey = getCacheKey();
			if (cacheKey != null) {
				DBTypes cachedType = DB_TYPE_CACHE.get(cacheKey);
				if (cachedType != null) {
					dbType = cachedType;
					return cachedType;
				}
			}

			Connection conn = null;
			boolean shouldCloseConn = true;
			try {
				if (this.factoryType == 4) {
					// 外部连接模式，不关闭连接
					conn = this.externalConnection;
					shouldCloseConn = false;
					if (conn == null) {
						JDBCLogger.getLogger().error("外部连接为null，无法获取数据库类型");
						return DBTypes.OTHER;
					}
					if (conn.isClosed()) {
						JDBCLogger.getLogger().error("外部连接已关闭，无法获取数据库类型");
						return DBTypes.OTHER;
					}
				} else {
					conn = this.getConnection();
				}

				String connUrl = conn.getMetaData().getURL().toLowerCase();
				dbType = determineDBType(connUrl);

				if (cacheKey != null) {
					DB_TYPE_CACHE.put(cacheKey, dbType);
				}

				return dbType;

			} catch (Exception ex) {
				JDBCLogger.getLogger().error("getDriverType() exception,cause:" + ex.getMessage(), ex);
				return DBTypes.OTHER;
			} finally {
				if (shouldCloseConn) {
					closeQuietly(conn);
				}
			}
		}
	}

	// 添加缓存key生成方法
	private String getCacheKey() {
		if (factoryType == 3 && sysDatasourceName != null) {
			return "sys:" + sysDatasourceName;
		}
		if (factoryType == 2 && appName != null && appDatasourceName != null) {
			try {
				AppContext appContext = AppContext.getContext(this.appName);
				String sysDsName = appContext.getDatasourceName(this.appDatasourceName);
				return "app:" + sysDsName;
			} catch (Exception e) {
				JDBCLogger.getLogger().error("Failed to get system datasource name", e);
			}
		}
		if (factoryType == 4 && externalConnection != null) {
			// 外部连接使用连接对象的hashCode作为缓存key
			return "external:" + externalConnection.hashCode();
		}
		return null;
	}

	// 添加缓存清理方法
	public static void clearDBTypeCache() {
		DB_TYPE_CACHE.clear();
	}

	public static void removeDBTypeCache(String appName, String appDatasourceName) {
		try {
			AppContext appContext = AppContext.getContext(appName);
			String sysDsName = appContext.getDatasourceName(appDatasourceName);
			DB_TYPE_CACHE.remove("app:" + sysDsName);
		} catch (Exception e) {
			JDBCLogger.getLogger().error("Failed to remove cache", e);
		}
	}

	public static void removeSysDBTypeCache(String sysDatasourceName) {
		DB_TYPE_CACHE.remove("sys:" + sysDatasourceName);
	}

	private DBTypes determineDBType(String url) {
		if (url.contains("mysql")) return DBTypes.MYSQL;
		if (url.contains("oracle")) return DBTypes.ORACLE;
		if (url.contains("postgresql")) return DBTypes.PostgreSql;
		if (url.contains("sybase")) return DBTypes.SYBASE;
		if (url.contains("db2")) return DBTypes.DB2;
		if (url.contains("sqlserver") || url.contains("sqljdbc")) return DBTypes.SQLSERVER;
		if (url.contains("sqlite")) return DBTypes.SQLITE;
		if (url.contains("opengauss")) return DBTypes.OPENGAUSS;
		if (url.contains("jdbc:dm")) return DBTypes.DAMENG;
		return DBTypes.OTHER;
	}

	private void closeQuietly(Connection conn) {
		if (conn != null) {
			try {
				conn.close();
			} catch (Exception e) {
				JDBCLogger.getLogger().error("Failed to closeQuietly", e);
			}
		}
	}
}
