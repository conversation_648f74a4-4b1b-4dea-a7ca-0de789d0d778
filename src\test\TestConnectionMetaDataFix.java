package test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.impl.EasyQueryImpl;

/**
 * Test ConnectionMetaData null pointer exception fix
 */
public class TestConnectionMetaDataFix {

    public static void main(String[] args) {
        testExternalConnectionMetaData();
        testEasyQueryImplWithExternalConnection();
    }

    /**
     * Test ConnectionMetaData with external connection
     */
    public static void testExternalConnectionMetaData() {
        System.out.println("=== Test ConnectionMetaData External Connection Support ===");

        Connection conn = null;
        try {
            // Create a mock database connection (using H2 in-memory database as example)
            conn = DriverManager.getConnection("jdbc:h2:mem:testdb", "sa", "");

            // Create ConnectionMetaData with external connection
            ConnectionMetaData metaData = new ConnectionMetaData(conn);

            // Test getting database type
            DBTypes dbType = metaData.getDriverType();
            System.out.println("Database type: " + dbType);

            // Test getting connection
            Connection testConn = metaData.getConnection();
            System.out.println("Connection retrieved successfully: " + (testConn != null));
            System.out.println("Is same connection object: " + (testConn == conn));

        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * Test EasyQueryImpl getTypes() method with external connection
     */
    public static void testEasyQueryImplWithExternalConnection() {
        System.out.println("\n=== Test EasyQueryImpl External Connection Support ===");

        Connection conn = null;
        try {
            // Create a mock database connection
            conn = DriverManager.getConnection("jdbc:h2:mem:testdb2", "sa", "");

            // Create EasyQueryImpl with external connection
            EasyQueryImpl easyQuery = new EasyQueryImpl(conn);

            // Test getTypes() method (previously would throw null pointer exception)
            DBTypes dbType = easyQuery.getTypes();
            System.out.println("EasyQueryImpl database type: " + dbType);

            // Test getting connection
            Connection testConn = easyQuery.getConnection();
            System.out.println("EasyQueryImpl connection retrieved successfully: " + (testConn != null));

            System.out.println("Fix successful! No more null pointer exception.");

        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
